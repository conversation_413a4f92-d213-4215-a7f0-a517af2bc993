/*
 * @Description: 来料检验实时看板
 */
import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import moment from 'moment';
import * as echarts from 'echarts';
import 'echarts-gl';
import { DataSet, DatePicker } from 'choerodon-ui/pro';
import { Icon } from 'choerodon-ui';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import Record from 'choerodon-ui/pro/lib/data-set/Record';
import { openTab } from 'utils/menuTab';
import styles from './index.module.less';
import './datepicker-fix.module.less'; // 引入DatePicker样式修复
import ECharts from './components/ECharts';
import { getPie3D, getParametricEquation } from './components/pieChartHelper';
import { dashboardService } from './services';
import {
  OverflowScroller,
  MaterialFilterModal,
  SupplierFilterModal,
  usePaginatedTableScroll,
} from './components';

import type {
  ProgressStatsData,
  DefectiveStatsData,
  MaterialStatsData,
  SupplierStatsData,
} from './services';

interface ExtendedSupplierStatsData extends SupplierStatsData {
  id?: number;
  isNewlyLoaded?: boolean;
}

interface ExtendedMaterialStatsData extends MaterialStatsData {
  id?: number;
  isNewlyLoaded?: boolean;
}

const IncomingInspectionDashboard = () => {
  const [formattedTime, setFormattedTime] = useState(moment().format('YYYY-MM-DD HH:mm:ss'));
  const [week, setWeek] = useState(moment().format('dddd'));

  const [startDate, setStartDate] = useState(moment().subtract(1, 'month').startOf('day'));
  const [endDate, setEndDate] = useState(moment().endOf('day'));
  const [isDataLoading, setIsDataLoading] = useState(false);

  const [activeDefectiveRow, setActiveDefectiveRow] = useState<number | null>(null);



  // 接口数据状态
  const [progressStats, setProgressStats] = useState<ProgressStatsData>({
    pending: 0,
    overdue: 0,
    inProgress: 0,
    completed: 0,
  });
  const [defectiveStats, setDefectiveStats] = useState<DefectiveStatsData[]>([]);

  // 分页数据状态管理
  const [defectivePageData, setDefectivePageData] = useState<any[]>([]);
  const [defectivePageInfo, setDefectivePageInfo] = useState<{
    current: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
    loading: boolean;
  }>({
    current: 0, // 修改为从0开始，符合API分页规范
    pageSize: 20,
    total: 0,
    hasMore: true,
    loading: false,
  });

  const [materialPageData, setMaterialPageData] = useState<ExtendedMaterialStatsData[]>([]);
  const [materialPageInfo, setMaterialPageInfo] = useState<{
    current: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
    loading: boolean;
  }>({
    current: 0, // 修改为从0开始，符合API分页规范
    pageSize: 20,
    total: 0,
    hasMore: true,
    loading: false,
  });

  const [supplierPageData, setSupplierPageData] = useState<ExtendedSupplierStatsData[]>([]);
  const [supplierPageInfo, setSupplierPageInfo] = useState<{
    current: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
    loading: boolean;
  }>({
    current: 0, // 修改为从0开始，符合API分页规范
    pageSize: 20,
    total: 0,
    hasMore: true,
    loading: false,
  });

  const [selectedMaterial, setSelectedMaterial] = useState<Record | null>(null);
  const [selectedSupplier, setSelectedSupplier] = useState<Record | null>(null);



  const [isMaterialModalOpen, setIsMaterialModalOpen] = useState(false);
  const [tempSelectedMaterial, setTempSelectedMaterial] = useState<Record | null>(null);

  const [isSupplierModalOpen, setIsSupplierModalOpen] = useState(false);
  const [tempSelectedSupplier, setTempSelectedSupplier] = useState<Record | null>(null);

  // 全屏状态管理
  const [isFullscreen, setIsFullscreen] = useState(false);

  const tableScrollRef = useRef<HTMLDivElement>(null);
  const materialTableScrollRef = useRef<HTMLDivElement>(null);
  const supplierTableScrollRef = useRef<HTMLDivElement>(null);
  const pieChartRef = useRef<HTMLDivElement>(null);

  // 分页加载函数
  const loadMoreDefectiveData = async () => {
    if (defectivePageInfo.loading || !defectivePageInfo.hasMore) return;

    setDefectivePageInfo(prev => ({ ...prev, loading: true }));

    try {
      // 调用实际的API
      const response = await dashboardService.getDefectiveDetails(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
        undefined, // 不筛选特定不良项目
        defectivePageInfo.current,
        defectivePageInfo.pageSize,
      );

      // 转换数据格式以匹配现有的显示结构
      const newData = response.content.map((item, index) => ({
        id: defectivePageInfo.current * defectivePageInfo.pageSize + index + 1,
        inspectionId: item.inspectionId,
        materialCode: item.materialCode,
        material: item.material,
        supplier: item.supplier,
        supplierCode: item.supplierCode,
        creationDate: item.creationDate,
        status: '检验完成',
        isPassed: false,
        defectiveItem: item.defectiveItem,
        inspector: item.inspector || '未知',
        arrivalBatch: `B${String(item.inspectDocId).slice(-3)}`,
        overdueDays: 0,
        isNewlyLoaded: true, // 标记新加载的数据
      }));

      setDefectivePageData(prev => [...prev, ...newData]);
      setDefectivePageInfo(prev => ({
        ...prev,
        current: prev.current + 1,
        loading: false,
        hasMore: response.number < response.totalPages - 1,
        total: response.totalElements,
      }));

      // 移除新加载标记，触发动画
      setTimeout(() => {
        setDefectivePageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
      }, 100);
    } catch (error) {
      console.error('加载不良信息数据失败:', error);
      setDefectivePageInfo(prev => ({ ...prev, loading: false }));
    }
  };

  const loadMoreMaterialData = async () => {
    if (materialPageInfo.loading || !materialPageInfo.hasMore) return;

    setMaterialPageInfo(prev => ({ ...prev, loading: true }));

    try {
      // 调用实际的API
      // 需要根据供应商编码找到对应的ID
      let supplierIdForFilter: number | undefined;
      if (selectedSupplier) {
        // 从供应商数据中找到对应的ID
        const supplierItem = supplierPageData.find(
          item => item.supplierCode === selectedSupplier.get('code'),
        );
        supplierIdForFilter = supplierItem?.supplierId;
      }

      const response = await dashboardService.getMaterialStats(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
        supplierIdForFilter, // 使用找到的供应商ID进行筛选
        materialPageInfo.current,
        materialPageInfo.pageSize,
      );

      // 转换数据格式并标记新加载的数据
      const newData = response.content.map((item, index) => ({
        ...item,
        id: materialPageInfo.current * materialPageInfo.pageSize + index + 1, // 前端生成连续序号
        isNewlyLoaded: true,
      }));

      setMaterialPageData(prev => [...prev, ...newData]);
      setMaterialPageInfo(prev => ({
        ...prev,
        current: prev.current + 1,
        loading: false,
        hasMore: response.number < response.totalPages - 1,
        total: response.totalElements,
      }));

      // 移除新加载标记，触发动画
      setTimeout(() => {
        setMaterialPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
      }, 100);
    } catch (error) {
      console.error('加载物料数据失败:', error);
      setMaterialPageInfo(prev => ({ ...prev, loading: false }));
    }
  };

  const loadMoreSupplierData = async () => {
    if (supplierPageInfo.loading || !supplierPageInfo.hasMore) return;

    setSupplierPageInfo(prev => ({ ...prev, loading: true }));

    try {
      // 调用实际的API
      // 需要根据物料编码找到对应的ID
      let materialIdForFilter: number | undefined;
      if (selectedMaterial) {
        // 从物料数据中找到对应的ID
        const materialItem = materialPageData.find(
          item => item.materialCode === selectedMaterial.get('code'),
        );
        materialIdForFilter = materialItem?.materialId;
      }

      const response = await dashboardService.getSupplierStats(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
        materialIdForFilter, // 使用找到的物料ID进行筛选
        supplierPageInfo.current,
        supplierPageInfo.pageSize,
      );

      // 转换数据格式并标记新加载的数据
      const newData = response.content.map((item, index) => ({
        ...item,
        id: supplierPageInfo.current * supplierPageInfo.pageSize + index + 1, // 前端生成连续序号
        isNewlyLoaded: true,
      }));

      setSupplierPageData(prev => [...prev, ...newData]);
      setSupplierPageInfo(prev => ({
        ...prev,
        current: prev.current + 1,
        loading: false,
        hasMore: response.number < response.totalPages - 1,
        total: response.totalElements,
      }));

      // 移除新加载标记，触发动画
      setTimeout(() => {
        setSupplierPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
      }, 100);
    } catch (error) {
      console.error('加载供应商数据失败:', error);
      setSupplierPageInfo(prev => ({ ...prev, loading: false }));
    }
  };


  const isInitializedRef = useRef(false);

  const loadAllData = useCallback(async () => {
    setIsDataLoading(true);

    try {
      setDefectivePageData([]);
      setDefectivePageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: true,
        loading: false,
      });

      setMaterialPageData([]);
      setMaterialPageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: true,
        loading: false,
      });

      setSupplierPageData([]);
      setSupplierPageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: true,
        loading: false,
      });

      await Promise.all([
        dashboardService.getProgressStats(
          startDate.format('YYYY-MM-DD'),
          endDate.format('YYYY-MM-DD')
        ).then(data => setProgressStats(data)),

        dashboardService.getDefectiveStats(
          startDate.format('YYYY-MM-DD'),
          endDate.format('YYYY-MM-DD')
        ).then(data => setDefectiveStats(data)),

        loadMoreDefectiveData(),
        loadMoreMaterialData(),
        loadMoreSupplierData(),
      ]);

      console.log('所有数据加载完成');
    } catch (error) {
      console.error('数据加载失败:', error);
    } finally {
      setIsDataLoading(false);
    }
  }, [startDate, endDate]);

  const toggleFullscreen = () => {
    const dashboardElement = document.getElementById('incomingInspectionDashboard');

    if (!isFullscreen) {
      // 进入全屏模式
      if (dashboardElement) {
        if (dashboardElement.requestFullscreen) {
          dashboardElement.requestFullscreen();
        } else if ((dashboardElement as any).mozRequestFullScreen) {
          (dashboardElement as any).mozRequestFullScreen();
        } else if ((dashboardElement as any).msRequestFullscreen) {
          (dashboardElement as any).msRequestFullscreen();
        } else if ((dashboardElement as any).webkitRequestFullscreen) {
          (dashboardElement as any).webkitRequestFullscreen();
        }
      }
    } else {
      // 退出全屏模式
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if ((document as any).msExitFullscreen) {
        (document as any).msExitFullscreen();
      } else if ((document as any).mozCancelFullScreen) {
        (document as any).mozCancelFullScreen();
      } else if ((document as any).webkitExitFullscreen) {
        (document as any).webkitExitFullscreen();
      }
    }
  };

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).msFullscreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);
    };

    // 添加全屏状态变化监听器
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      // 清理监听器
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  // 初始化和时间变化处理 - 合并为一个useEffect
  useEffect(() => {
    if (!isInitializedRef.current) {
      // 首次初始化，立即加载
      console.log('组件初始化，加载数据...');
      isInitializedRef.current = true;
      loadAllData();
    } else {
      // 时间变化，防抖处理
      console.log('时间范围变化:', startDate.format('YYYY-MM-DD'), '到', endDate.format('YYYY-MM-DD'));
      const timer = setTimeout(() => {
        console.log('时间变化，重新加载数据...');
        loadAllData();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [startDate, endDate, loadAllData]);

  const pieChartData = useMemo(() => [
    { name: '检验完成', value: progressStats.completed, itemStyle: { color: '#4a90e2' } },
    { name: '待检验', value: progressStats.pending, itemStyle: { color: '#f5a623' } },
    { name: '超期待检', value: progressStats.overdue, itemStyle: { color: '#d0021b' } },
    { name: '检验中', value: progressStats.inProgress, itemStyle: { color: '#50e3c2' } },
  ], [progressStats]);

  // 使用真实的不良统计数据
  const defectiveStatsData = useMemo(() => {
    if (!defectiveStats.length) {
      return {
        categories: [''],
        counts: [{ value: 0, itemStyle: { color: 'transparent' } }],
        ratios: [null]
      };
    }

    // 在数据前后添加空白项以改善视觉效果
    const categories = ['', ...defectiveStats.map(item => item.name), ''];
    const counts = [
      { value: 0, itemStyle: { color: 'transparent' } },
      ...defectiveStats.map(item => item.count),
      { value: 0, itemStyle: { color: 'transparent' } },
    ];
    const ratios = [null, ...defectiveStats.map(item => item.ratio / 100), null];

    return { categories, counts, ratios };
  }, [defectiveStats]);



  // 使用分页滚动hook
  usePaginatedTableScroll(
    tableScrollRef,
    defectivePageData,
    defectivePageInfo,
    loadMoreDefectiveData,
    2000,
  );
  usePaginatedTableScroll(
    materialTableScrollRef,
    materialPageData,
    materialPageInfo,
    loadMoreMaterialData,
    2000,
  );
  usePaginatedTableScroll(
    supplierTableScrollRef,
    supplierPageData,
    supplierPageInfo,
    loadMoreSupplierData,
    2000,
  );

  const onDefectiveChartClick = (params: any) => {
    if (params.componentType === 'series' && params.seriesType === 'bar') {
      const projectName = params.name;
      // 跳过空的占位项
      if (!projectName) return;

      const clickedData = defectivePageData.find(d => d.defectiveItem === projectName);
      if (clickedData) {
        setActiveDefectiveRow(clickedData.id as any);
        setTimeout(() => {
          setActiveDefectiveRow(null);
        }, 5000);
      }
    }
  };

  // 处理检验单号点击跳转
  const handleInspectionIdClick = (inspectionId: string) => {
    // 跳转到检验平台的全部页签
    openTab({
      title: '检验平台',
      key: '/hwms/inspection-platform-wg/list',
      path: '/hwms/inspection-platform-wg/list',
      state: {
        sourceDocId: inspectionId,
        sourceDocNum: inspectionId,
      },
    });
  };

  // 使用真实的物料数据
  const allMaterials = materialPageData.map(item => ({
    ...item,
    code: item.materialCode,
    description: item.material,
  }));

  // 使用真实的供应商数据
  const allSuppliers = supplierPageData.map(item => ({
    ...item,
    code: item.supplierCode,
    description: item.supplier,
  }));

  // 简化 DataSet 创建
  const materialDataSet = new DataSet({
    data: allMaterials,
    fields: [
      { name: 'code', type: FieldType.string, label: '物料编码' },
      { name: 'description', type: FieldType.string, label: '物料描述' },
    ],
    selection: DataSetSelection.single,
  });

  const supplierDataSet = new DataSet({
    data: allSuppliers,
    fields: [
      { name: 'code', type: FieldType.string, label: '供应商编码' },
      { name: 'description', type: FieldType.string, label: '供应商描述' },
    ],
    selection: DataSetSelection.single,
  });

  const openMaterialModal = (() => {
      setTempSelectedMaterial(selectedMaterial);
      setIsMaterialModalOpen(true);
  });

  const handleMaterialSelect = (record: any) => {
    setTempSelectedMaterial(record);
  };

  const handleMaterialConfirm = async () => {
    setSelectedMaterial(tempSelectedMaterial);
    if (tempSelectedMaterial) {
      setSelectedSupplier(null);

      try {
        // 根据选中的物料，重新加载供应商数据（只显示该物料对应的供应商）
        const materialId = tempSelectedMaterial.get('materialId') ||
                          materialPageData.find(item => item.materialCode === tempSelectedMaterial.get('code'))?.materialId;

        if (materialId) {
          // 重置供应商数据
          setSupplierPageData([]);
          setSupplierPageInfo({
            current: 0,
            pageSize: 20,
            total: 0,
            hasMore: true,
            loading: false,
          });

          // 加载该物料对应的供应商
          const response = await dashboardService.getSupplierStats(
            startDate.format('YYYY-MM-DD'),
            endDate.format('YYYY-MM-DD'),
            materialId,
            0,
            20
          );

          if (response?.content) {
            const newData = response.content.map((item: any, index: number) => ({
              ...item,
              id: index + 1,
              isNewlyLoaded: true,
            }));

            setSupplierPageData(newData);
            setSupplierPageInfo({
              current: 1,
              pageSize: 20,
              total: response.totalElements,
              hasMore: response.number < response.totalPages - 1,
              loading: false,
            });

            // 移除新加载标记
            setTimeout(() => {
              setSupplierPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
            }, 100);
          }
        }
      } catch (error) {
        console.error('加载物料对应的供应商数据失败:', error);
      }
    }
    setIsMaterialModalOpen(false);
  };

  const handleMaterialCancel = () => {
    setIsMaterialModalOpen(false);
    setTempSelectedMaterial(null);
  };

  // 处理物料查询
  const handleMaterialQuery = async () => {
    try {
      // 重置物料数据并重新加载
      setMaterialPageData([]);
      setMaterialPageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: true,
        loading: false,
      });

      // 重置供应商数据并重新加载
      setSupplierPageData([]);
      setSupplierPageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: true,
        loading: false,
      });

      // 重新加载数据（这里可以根据筛选条件调用相应的API）
      await Promise.all([
        loadMoreMaterialData(),
        loadMoreSupplierData()
      ]);
    } catch (error) {
      console.error('查询物料数据失败:', error);
    }
  };

  const openSupplierModal = () => {
    setTempSelectedSupplier(selectedSupplier);
    setIsSupplierModalOpen(true);
  };

  const handleSupplierSelect = (record: any) => {
    setTempSelectedSupplier(record);
  };

  const handleSupplierConfirm = async () => {
    setSelectedSupplier(tempSelectedSupplier);
    if (tempSelectedSupplier) {
      setSelectedMaterial(null);

      try {
        // 根据选中的供应商，重新加载物料数据（只显示该供应商对应的物料）
        const supplierId = tempSelectedSupplier.get('supplierId') ||
                          supplierPageData.find(item => item.supplierCode === tempSelectedSupplier.get('code'))?.supplierId;

        if (supplierId) {
          // 重置物料数据
          setMaterialPageData([]);
          setMaterialPageInfo({
            current: 0,
            pageSize: 20,
            total: 0,
            hasMore: true,
            loading: false,
          });

          // 加载该供应商对应的物料
          const response = await dashboardService.getMaterialStats(
            startDate.format('YYYY-MM-DD'),
            endDate.format('YYYY-MM-DD'),
            supplierId,
            0,
            20
          );

          if (response?.content) {
            const newData = response.content.map((item: any, index: number) => ({
              ...item,
              id: index + 1,
              isNewlyLoaded: true,
            }));

            setMaterialPageData(newData);
            setMaterialPageInfo({
              current: 1,
              pageSize: 20,
              total: response.totalElements,
              hasMore: response.number < response.totalPages - 1,
              loading: false,
            });

            // 移除新加载标记
            setTimeout(() => {
              setMaterialPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
            }, 100);
          }
        }
      } catch (error) {
        console.error('加载供应商对应的物料数据失败:', error);
      }
    }
    setIsSupplierModalOpen(false);
  };

  const handleSupplierCancel = () => {
    setIsSupplierModalOpen(false);
    setTempSelectedSupplier(null);
  };

  // 处理供应商查询
  const handleSupplierQuery = async () => {
    try {
      // 重置供应商数据并重新加载
      setSupplierPageData([]);
      setSupplierPageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: true,
        loading: false,
      });

      // 重置物料数据并重新加载
      setMaterialPageData([]);
      setMaterialPageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: true,
        loading: false,
      });

      // 重新加载数据（这里可以根据筛选条件调用相应的API）
      await Promise.all([
        loadMoreMaterialData(),
        loadMoreSupplierData()
      ]);
    } catch (error) {
      console.error('查询供应商数据失败:', error);
    }
  };

  // 处理供应商数据导出
  const handleSupplierExport = async () => {
    try {
      // 获取当前选中的物料ID（如果有筛选条件）
      let materialIdForExport: number | undefined;
      if (selectedMaterial) {
        const materialItem = materialPageData.find(
          item => item.materialCode === selectedMaterial.get('code'),
        );
        materialIdForExport = materialItem?.materialId;
      }

      await dashboardService.exportSupplierStats(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
        materialIdForExport
      );
    } catch (error) {
      console.error('导出供应商数据失败:', error);
      // 可以在这里添加错误提示
    }
  };

  useEffect(() => {
    const timer = setInterval(() => {
      setFormattedTime(moment().format('YYYY-MM-DD HH:mm:ss'));
      setWeek(moment().format('dddd'));
    }, 1000);
    return () => clearInterval(timer);
  }, []);



  // 3D饼图初始化
  useEffect(() => {
    if (pieChartRef.current) {
      const myChart = echarts.init(pieChartRef.current);
      const option: any = getPie3D(pieChartData, 0.8);

      // 根据全屏状态调整3D图表的中心位置
      if (option.grid3D) {
        option.grid3D.center = isFullscreen ? ['50%', '42%'] : ['50%', '37%'];
        option.grid3D.top = isFullscreen ? '-8%' : '-13%';
      }

      // 添加图例配置
      const optionData = pieChartData.map(item => ({
        name: item.name,
        value: item.value,
        itemStyle: item.itemStyle,
      }));

      option.legend = {
        show: true,
        bottom: '2%',
        left: 'center',
        textStyle: {
          color: '#fff',
          fontSize: 12,
        },
        itemGap: 20,
        itemWidth: 15,
        itemHeight: 15,
        data: optionData.map(item => ({
          name: item.name,
          icon: 'rect',
        })),
      };

      // 添加2D饼图用于标签显示
      option.series.push({
        name: 'pie2d',
        type: 'pie',
        labelLine: {
          show: true,
          length: 30,
          length2: 60,
          smooth: false,
          lineStyle: {
            color: '#fff',
            width: 1,
          },
        },
        startAngle: -20,
        clockwise: false,
        radius: ['35%', '40%'],
        center: isFullscreen ? ['50%', '42%'] : ['50%', '37%'],
        data: optionData,
        itemStyle: {
          opacity: 0,
        },
        label: {
          show: true,
          position: 'outside',
          distanceToLabelLine: 5,
          alignTo: 'labelLine',
          bleedMargin: 5,
          formatter: (params: any) => {
            const item = optionData.find(i => i.name === params.name);
            if (!item) {
              return '';
            }
            const bfb = ((item.value / pieChartData.reduce((a, b) => a + b.value, 0)) * 100).toFixed(0);
            const colorKey = `color_${params.dataIndex}`;
            return `{${colorKey}|${bfb}%}\n{name|${params.name}}`;
          },
          rich: (() => {
            const richConfig: any = {
              name: {
                color: '#fff',
                fontSize: 12,  // 缩小标签名称字体
                lineHeight: 16,
              },
            };
            optionData.forEach((item, index) => {
              richConfig[`color_${index}`] = {
                fontSize: 14,  // 缩小百分比字体
                lineHeight: 18,
                fontWeight: 'bold',
                color: item.itemStyle?.color || '#fff',
              };
            });
            return richConfig;
          })(),
        },
      });

      myChart.setOption(option);

      let selectedIndex = '';
      let hoveredIndex = '';

      // 点击事件
      myChart.on('click', function(params: any) {
        if (!myChart) return;
        const seriesIndex = params.seriesIndex;
        if (params.seriesName === 'pie2d') return;
        if (option.series[seriesIndex] && option.series[seriesIndex].pieStatus) {
          const isSelected = !option.series[seriesIndex].pieStatus.selected;
          const isHovered = option.series[seriesIndex].pieStatus.hovered;
          const k = option.series[seriesIndex].pieStatus.k;
          const startRatio = option.series[seriesIndex].pieData.startRatio;
          const endRatio = option.series[seriesIndex].pieData.endRatio;

          if (selectedIndex !== '' && selectedIndex !== `${seriesIndex}`) {
            const oldSelectedIndex = parseInt(selectedIndex, 10);
            option.series[oldSelectedIndex].parametricEquation = getParametricEquation(
              option.series[oldSelectedIndex].pieData.startRatio,
              option.series[oldSelectedIndex].pieData.endRatio,
              false,
              false,
              k,
              option.series[oldSelectedIndex].pieData.value,
            );
            option.series[oldSelectedIndex].pieStatus.selected = false;
          }

          option.series[seriesIndex].parametricEquation = getParametricEquation(
            startRatio,
            endRatio,
            isSelected,
            isHovered,
            k,
            option.series[seriesIndex].pieData.value,
          );
          option.series[seriesIndex].pieStatus.selected = isSelected;

          if (isSelected) {
            selectedIndex = `${seriesIndex}`;
          } else {
            selectedIndex = '';
          }
          myChart.setOption(option);
        }
      });

      // 鼠标悬停事件
      myChart.on('mouseover', function(params: any) {
        if (!myChart) return;
        const seriesIndex = params.seriesIndex;
        if (hoveredIndex === `${seriesIndex}`) {
          return;
        }

        if (hoveredIndex !== '') {
          const oldHoveredIndex = parseInt(hoveredIndex, 10);
          if (option.series[oldHoveredIndex] && option.series[oldHoveredIndex].pieStatus) {
            const isSelected = option.series[oldHoveredIndex].pieStatus.selected;
            const k = option.series[oldHoveredIndex].pieStatus.k;
            option.series[oldHoveredIndex].parametricEquation = getParametricEquation(
              option.series[oldHoveredIndex].pieData.startRatio,
              option.series[oldHoveredIndex].pieData.endRatio,
              isSelected,
              false,
              k,
              option.series[oldHoveredIndex].pieData.value,
            );
            option.series[oldHoveredIndex].pieStatus.hovered = false;
            hoveredIndex = '';
          }
        }

        if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
          if (option.series[seriesIndex] && option.series[seriesIndex].pieStatus) {
            const isSelected = option.series[seriesIndex].pieStatus.selected;
            const k = option.series[seriesIndex].pieStatus.k;
            option.series[seriesIndex].parametricEquation = getParametricEquation(
              option.series[seriesIndex].pieData.startRatio,
              option.series[seriesIndex].pieData.endRatio,
              isSelected,
              true,
              k,
              option.series[seriesIndex].pieData.value + 5,
            );
            option.series[seriesIndex].pieStatus.hovered = true;
            hoveredIndex = `${seriesIndex}`;
          }
        }
        myChart.setOption(option);
      });

      // 鼠标离开事件
      myChart.on('globalout', function() {
        if (!myChart) return;
        if (hoveredIndex !== '') {
          const oldHoveredIndex = parseInt(hoveredIndex, 10);
          if (option.series[oldHoveredIndex] && option.series[oldHoveredIndex].pieStatus) {
            const isSelected = option.series[oldHoveredIndex].pieStatus.selected;
            const k = option.series[oldHoveredIndex].pieStatus.k;
            option.series[oldHoveredIndex].parametricEquation = getParametricEquation(
              option.series[oldHoveredIndex].pieData.startRatio,
              option.series[oldHoveredIndex].pieData.endRatio,
              isSelected,
              false,
              k,
              option.series[oldHoveredIndex].pieData.value,
            );
            option.series[oldHoveredIndex].pieStatus.hovered = false;
            hoveredIndex = '';
          }
        }
        myChart.setOption(option);
      });

      // 窗口大小变化时重新调整图表
      const handleResize = () => {
        if (myChart) {
          myChart.resize();
          // 全屏切换时重新调整图表中心位置
          const currentOption = myChart.getOption();
          if (currentOption.grid3D && currentOption.grid3D[0]) {
            currentOption.grid3D[0].center = isFullscreen ? ['50%', '42%'] : ['50%', '37%'];
            currentOption.grid3D[0].top = isFullscreen ? '-8%' : '-13%';
          }
          // 调整2D饼图中心位置
          if (currentOption.series) {
            const pie2dSeries = currentOption.series.find((s: any) => s.name === 'pie2d');
            if (pie2dSeries) {
              pie2dSeries.center = isFullscreen ? ['50%', '42%'] : ['50%', '37%'];
            }
          }
          myChart.setOption(currentOption);
        }
      };

      // 全屏状态变化时立即调整图表
      const handleFullscreenChange = () => {
        setTimeout(() => {
          if (myChart) {
            const currentOption = myChart.getOption();
            // 调整3D图表中心位置
            if (currentOption.grid3D && currentOption.grid3D[0]) {
              currentOption.grid3D[0].center = isFullscreen ? ['50%', '42%'] : ['50%', '37%'];
              currentOption.grid3D[0].top = isFullscreen ? '-8%' : '-13%';
            }
            // 调整2D饼图中心位置
            if (currentOption.series) {
              const pie2dSeries = currentOption.series.find((s: any) => s.name === 'pie2d');
              if (pie2dSeries) {
                pie2dSeries.center = isFullscreen ? ['50%', '42%'] : ['50%', '37%'];
              }
            }
            myChart.setOption(currentOption);
            myChart.resize();
          }
        }, 100);
      };

      window.addEventListener('resize', handleResize);
      document.addEventListener('fullscreenchange', handleFullscreenChange);

      return () => {
        window.removeEventListener('resize', handleResize);
        document.removeEventListener('fullscreenchange', handleFullscreenChange);
        myChart?.dispose();
      };
    }
  }, [pieChartData, isFullscreen]);



  // 移除重复的定时器设置，已在上面定义过

  const defectiveStatsOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#777',
      textStyle: {
        color: '#fff',
      },
      formatter: (params: any) => {
        // 过滤掉空的占位项和null值
        const validParams = params.filter((param: any) => {
          return param.name !== '' && param.value !== null && param.value !== undefined;
        });
        if (validParams.length === 0) return '';

        const name = validParams[0].name;
        let result = `${name}<br/>`;

        validParams.forEach((param: any) => {
          const marker = param.marker;
          const seriesName = param.seriesName;
          let value = param.value;

          if (typeof value === 'object' && value.value !== undefined) {
            value = value.value;
          }

          if (seriesName === '不良个数') {
            result += `${marker}${seriesName}: ${value}个<br/>`;
          } else if (seriesName === '不良占比') {
            result += `${marker}${seriesName}: ${(value * 100).toFixed(2)}%<br/>`;
          }
        });

        return result;
      },
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '10%',
      containLabel: true,
    },
    legend: {
      data: ['不良个数', '不良占比'],
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
      itemGap: 20,
      bottom: '5%',
    },
    xAxis: [
      {
        type: 'category',
        data: defectiveStatsData.categories,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          color: '#fff',
          interval: 0,
          fontSize: 12,
          rotate: defectiveStatsData.categories.some(c => c.length > 5) ? 30 : 0,
          align: 'center',
        },
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: '#fff',
          },
        },
        axisLine: {
          lineStyle: {
            color: '#fff',
          },
        },
        boundaryGap: true,
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed',
          },
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        min: 0,
        max: 10,
        interval: 1,
        axisLabel: {
          formatter: '{value}',
          color: '#fff',
          fontSize: 12,
        },
        axisLine: {
          lineStyle: {
            color: '#fff',
          },
        },
        axisTick: {
          lineStyle: {
            color: '#fff',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            type: 'dashed',
          },
        },
      },
      {
        type: 'value',
        name: '',
        min: 0,
        max: 1.0,
        interval: 0.1,
        axisLabel: {
          formatter(value: number) {
            return `${(value * 100).toFixed(2)}%`;
          },
          color: '#fff',
          fontSize: 12,
        },
        axisLine: {
          lineStyle: {
            color: '#fff',
          },
        },
        axisTick: {
          lineStyle: {
            color: '#fff',
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '不良个数',
        type: 'bar',
        yAxisIndex: 0,
        barWidth: '40%',
        barCategoryGap: '30%',
        showBackground: false,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgba(255, 154, 46, 0.2)' },
            { offset: 0.5, color: 'rgba(255, 154, 46, 0.8)' },
            { offset: 1, color: '#ff9a2e' },
          ]),
          borderRadius: [4, 4, 0, 0],
          shadowColor: 'rgba(255, 154, 46, 0.3)',
          shadowBlur: 10,
          shadowOffsetY: 2,
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          color: '#fff',
          fontSize: 12,
          fontWeight: 'bold',
        },
        tooltip: {
          valueFormatter(value: number) {
            return `${value}个`;
          },
        },
        data: defectiveStatsData.counts,
      },
      {
        name: '不良占比',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          color: '#00ffc5',
          width: 2,
        },
        itemStyle: {
          color: '#00ffc5',
          borderColor: '#fff',
          borderWidth: 2,
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(0, 255, 197, 0.5)',
            },
            {
              offset: 1,
              color: 'rgba(0, 255, 197, 0)',
            },
          ]),
        },

        label: {
          show: true,
          position: 'top',
          formatter: (params: any) => {
            return params.value === null ? '' : `${(params.value * 100).toFixed(2)}%`;
          },
          color: '#00ffc5',
          fontSize: 12,
          fontWeight: 'bold',
        },
        data: defectiveStatsData.ratios,
      },
    ],
  };

  return (
    <div
      id="incomingInspectionDashboard"
      className={`${styles.boardContainer} ${styles.bgBac} ${isFullscreen ? styles.fullscreen : ''}`}
      style={{
        backgroundColor: '#0d1224',
        color: '#fff',
        height: '100vh',
        position: 'relative',
        boxSizing: 'border-box',
        fontFamily: "'Microsoft YaHei', sans-serif",
      }}
    >
      <header className={styles.header}>
        <div className={styles.headerLeft}>
          <div
            className={`${styles.datePickerWrapper} ${styles.bgTimeBac}`}
          >
            <DatePicker
              className="custom-date-picker"
              popupCls="custom-datepicker-popup"
              placeholder="选择开始时间"
              value={startDate}
              onChange={date => setStartDate(date)}
              disabled={isDataLoading}
              style={{ background: 'transparent', border: 'none' }}
              clearButton={false}
            />
          </div>
          <div
            className={`${styles.datePickerWrapper} ${styles.bgTimeBac}`}
          >
            <DatePicker
              className="custom-date-picker"
              popupCls="custom-datepicker-popup"
              placeholder="选择结束时间"
              value={endDate}
              onChange={date => setEndDate(date)}
              disabled={isDataLoading}
              style={{ background: 'transparent', border: 'none' }}
              clearButton={false}
            />
          </div>
          {isDataLoading && (
            <div style={{
              color: '#4a90e2',
              fontSize: '14px',
              marginLeft: '16px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <div style={{
                width: '16px',
                height: '16px',
                border: '2px solid #4a90e2',
                borderTop: '2px solid transparent',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} />
              数据加载中...
            </div>
          )}
        </div>
        <div className={styles.headerCenter}>
          <div
            className={`${styles.title} ${styles.bgHeaderTitle} ${styles.headerTitle}`}
          />
          <div className={styles.enTitle}>DIGTAL VISUAL MONITORING SYSTEM</div>
        </div>
        <div className={styles.headerRight}>
          <div className={styles.timeWrapper}>
            <div className={styles.time}>{moment(formattedTime).format('HH:mm:ss')}</div>
            <div className={styles.date}>{moment(formattedTime).format('YYYY-MM-DD')}</div>
            <div className={styles.week}>{week}</div>
          </div>
          <div className={styles.fullscreenButton} onClick={toggleFullscreen}>
            <Icon type={isFullscreen ? "fullscreen_exit" : "fullscreen"} />
          </div>
        </div>
      </header>
      <main className={styles.mainContent}>
        <div className={`${styles.topRow} ${isFullscreen ? styles.fullscreenTopRow : ''}`}>
          <div className={styles.panel}>
            <div className={styles.panelHeader} style={{ padding: '4rem 0px 0px 5rem' }}>
              <div className={styles.panelTitle}>来料检验进度图</div>
            </div>
            <div className={`${styles.panelBody} ${styles.chartPanelBody}`}>
              <div
                className={`${styles.pieChartBackground} ${styles.bgPieBac}`}
              />
              <div ref={pieChartRef} style={{ width: '100%', height: '100%' }} />
            </div>
          </div>
          <div className={styles.panel}>
            <div className={styles.panelHeader} style={{ padding: '0rem 0px 0px 3.5rem' }}>
              <div className={styles.panelTitle}>来料检验不良项目统计图</div>
            </div>
            <div className={`${styles.panelBody} ${styles.chartPanelBody}`}>
              <ECharts
                option={defectiveStatsOption}
                style={{ width: '100%', height: '100%', maxHeight: '100%' }}
                onEvents={{ click: onDefectiveChartClick }}
                forceFullWidth
                notMerge={false}
                lazyUpdate={false}
              />
            </div>
          </div>
          <div className={styles.panel}>
            <div className={styles.panelHeader} style={{ padding: isFullscreen ? '4rem 0px 0px 3.8rem' : '4rem 0px 0px 2.8rem' }}>
              <div className={styles.panelTitle}>不良项目信息</div>
            </div>
            <div className={styles.panelBody} style={{ padding: '0' }}>
              <div className={styles.customTable}>
                <div
                  className={`${styles.tableHeader} ${styles.bgTableTitle}`}
                >
                  <span>不良项目</span>
                  <span>检验单号</span>
                  <span>物料</span>
                  <span>供应商</span>
                  <span>下达时间</span>
                  <span>检验员</span>
                </div>
                <div className={styles.tableBody} ref={tableScrollRef}>
                  {defectivePageData.map(item => (
                    <div
                      className={`${styles.tableRow} ${
                        activeDefectiveRow === item.id ? styles.activeRow : ''
                      } ${item.isNewlyLoaded ? styles.entering : ''}`}
                      key={item.id}
                    >
                      <OverflowScroller>{item.defectiveItem}</OverflowScroller>
                      <OverflowScroller>
                        <span
                          className={styles.clickableLink}
                          onClick={() => handleInspectionIdClick(item.inspectionId)}
                          style={{ color: '#c3d1ff' }}
                        >
                          {item.inspectionId}
                        </span>
                      </OverflowScroller>
                      <OverflowScroller>{item.material}</OverflowScroller>
                      <OverflowScroller>{item.supplier}</OverflowScroller>
                      <OverflowScroller>{item.creationDate}</OverflowScroller>
                      <OverflowScroller>{item.inspector}</OverflowScroller>
                    </div>
                  ))}
                  {defectivePageInfo.loading && (
                    <div className={styles.loadingRow}>
                      <span>加载中...</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className={`${styles.bottomRow} ${isFullscreen ? styles.fullscreenBottomRow : ''}`}>
          <div className={styles.panel} style={{ paddingTop: '3rem' }}>
            <div className={styles.panelHeader} style={{ padding: '2.4rem 0px 0px 5rem' }}>
              <div className={styles.panelTitle}>来料检验-物料</div>
              <div className={styles.panelExtra}>
                <div
                  className={`${styles.assetButton} ${styles.bgButtonBac}`}
                  onClick={openMaterialModal}
                >
                  物料筛选
                </div>
              </div>
            </div>
            <div className={styles.panelBody} style={{ padding: '0 0 0 1.5rem' }}>
              <div className={styles.customTable}>
                <div
                  className={`${styles.tableHeader2} ${styles.bgTableTitle}`}
                >
                  <span>序号</span>
                  <span>物料</span>
                  <span>物料到货批</span>
                  <span>物料合格率</span>
                </div>
                <div className={styles.tableBody} ref={materialTableScrollRef}>
                  {materialPageData.map(item => (
                    <div
                      className={`${styles.tableRow2} ${item.isNewlyLoaded ? styles.entering : ''}`}
                      key={item.id}
                    >
                      <OverflowScroller>
                        <div
                          className={`${styles.tableIcon} ${styles.bgBottomContentNum}`}
                        >
                          <span style={{
                            fontSize: '10px',
                            color: '#fff',
                            lineHeight: '20px',
                          }}>{item.id}</span>
                        </div>
                      </OverflowScroller>
                      <OverflowScroller>{item.material}</OverflowScroller>
                      <OverflowScroller >
                        {item.arrivalBatchCount}
                      </OverflowScroller>
                      <OverflowScroller >
                        {item.passRate}
                      </OverflowScroller>
                    </div>
                  ))}
                  {materialPageInfo.loading && (
                    <div className={styles.loadingRow}>
                      <span>加载中...</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className={styles.panel} style={{ paddingTop: '3rem' }}>
            <div className={styles.panelHeader} style={{ padding: isFullscreen ? '1.8rem 0 0 2.8rem' : '1.8rem 0 0 2rem' }}>
              <div className={styles.panelTitle}>来料检验-供应商</div>
              <div className={styles.panelExtra}>
                <div
                  className={`${styles.assetButton} ${styles.bgButtonBac}`}
                  onClick={openSupplierModal}
                >
                  供应商筛选
                </div>
                <div
                  className={`${styles.assetButton} ${styles.bgButtonBac}`}
                  onClick={handleSupplierExport}
                >
                  导出
                </div>
              </div>
            </div>
            <div className={styles.panelBody} style={{ padding: '0' }}>
              <div className={styles.customTable}>
                <div
                  className={`${styles.tableHeader2} ${styles.bgTableTitle}`}
                >
                  <span>序号</span>
                  <span>供应商</span>
                  <span>供应商到货批</span>
                  <span>供应商合格率</span>
                </div>
                <div className={styles.tableBody} ref={supplierTableScrollRef}>
                  {supplierPageData.map(item => (
                    <div
                      className={`${styles.tableRow2} ${item?.isNewlyLoaded ? styles.entering : ''}`}
                      key={item?.id}
                    >
                      <OverflowScroller>
                        <div
                          className={`${styles.tableIcon} ${styles.bgBottomContentNum}`}
                          style={{
                            margin: '0 8px',
                          }}
                        >
                          <span style={{
                            fontSize: '10px',
                            color: '#fff',
                            fontWeight: 'bold',
                            lineHeight: '20px',
                          }}>{item.id}</span>
                        </div>
                      </OverflowScroller>
                      <OverflowScroller>{item.supplier}</OverflowScroller>
                      <OverflowScroller >
                        {item.arrivalBatchCount}
                      </OverflowScroller>
                      <OverflowScroller >
                        {item.passRate}
                      </OverflowScroller>
                    </div>
                  ))}
                  {supplierPageInfo.loading && (
                    <div className={styles.loadingRow}>
                      <span>加载中...</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <MaterialFilterModal
        isOpen={isMaterialModalOpen}
        dataSet={materialDataSet}
        selectedRecord={tempSelectedMaterial}
        onSelect={handleMaterialSelect}
        onConfirm={handleMaterialConfirm}
        onCancel={handleMaterialCancel}
        materialPageData={materialPageData}
        onQuery={handleMaterialQuery}
      />

      <SupplierFilterModal
        isOpen={isSupplierModalOpen}
        dataSet={supplierDataSet}
        selectedRecord={tempSelectedSupplier}
        onSelect={handleSupplierSelect}
        onConfirm={handleSupplierConfirm}
        onCancel={handleSupplierCancel}
        supplierPageData={supplierPageData}
        onQuery={handleSupplierQuery}
      />
    </div>
  );
};

export default IncomingInspectionDashboard;
